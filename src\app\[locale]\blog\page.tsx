import { Metadata } from 'next';
// import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';

import { BlogList, BlogListSkeleton } from '@/components/blog';
import { DatabaseService } from '@/lib/database-service';
import { BlogPost, BlogCategory, BlogTag, Locale } from '@/types';

interface BlogPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    page?: string;
    category?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({
  params,
}: BlogPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });

  return {
    title: t('title'),
    description: t('description'),
    keywords: [
      t('keywords.mystical'),
      t('keywords.tarot'),
      t('keywords.astrology'),
      t('keywords.numerology'),
      t('keywords.crystal'),
      t('keywords.palmistry'),
      t('keywords.dreams'),
    ],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 获取博客数据的函数
async function getBlogData(
  locale: Locale,
  page: number = 1,
  limit: number = 12,
  filters?: {
    category?: string;
    tag?: string;
    search?: string;
  }
): Promise<{
  posts: BlogPost[];
  featuredPost?: BlogPost;
  totalPages: number;
  categories: BlogCategory[];
  tags: BlogTag[];
  totalPosts: number;
}> {
  try {
    // 获取分类和标签
    const [categories, tags] = await Promise.all([
      DatabaseService.getBlogCategories(locale),
      DatabaseService.getBlogTags(locale),
    ]);

    // 计算偏移量
    const offset = (page - 1) * limit;

    // 获取特色文章（仅在第一页显示）
    let featuredPost: BlogPost | undefined;
    if (page === 1) {
      const featuredPosts = await DatabaseService.getBlogPosts({
        locale,
        status: 'PUBLISHED',
        featured: true,
        limit: 1,
        orderBy: 'publishedAt',
        orderDirection: 'desc',
      });
      featuredPost = featuredPosts[0];
    }

    // 获取常规文章列表
    const posts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      ...(filters?.category && { category: filters.category }),
      featured: false, // 排除特色文章
      limit,
      offset,
      orderBy: 'publishedAt',
      orderDirection: 'desc',
    });

    // 获取总数用于分页
    const allPosts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      ...(filters?.category && { category: filters.category }),
      featured: false,
    });
    const totalPosts = allPosts.length;
    const totalPages = Math.ceil(totalPosts / limit);

    return {
      posts,
      ...(featuredPost && { featuredPost }),
      totalPages,
      categories,
      tags,
      totalPosts,
    };
  } catch (error) {
    console.error('Error fetching blog data:', error);
    // 返回空数据作为fallback
    return {
      posts: [],
      totalPages: 1,
      categories: [],
      tags: [],
      totalPosts: 0,
    };
  }
}

export default async function BlogPage({
  params,
  searchParams,
}: BlogPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const page = parseInt(searchParams.page || '1', 10);
  const { posts, featuredPost, totalPages, categories, tags, totalPosts } =
    await getBlogData(locale, page, 12, {
      category: searchParams.category,
      tag: searchParams.tag,
      search: searchParams.search,
    });

  return (
    <div className='min-h-screen bg-white dark:bg-dark-900'>
      {/* 页面头部 - 基于01规范的设计 */}
      <header className='mx-auto max-w-6xl px-4 py-12 sm:px-6 lg:px-8'>
        <div className='mb-8 text-center'>
          <h1 className='mb-4 font-serif text-3xl font-bold tracking-tight text-mystical-900 dark:text-white sm:text-4xl lg:text-5xl'>
            {t('title')}
          </h1>
          <p className='mx-auto max-w-2xl text-lg leading-relaxed text-mystical-600 dark:text-mystical-300 sm:text-xl'>
            {t('description')}
          </p>
        </div>

        {/* 分类导航 - Medium风格 */}
        <nav className='mb-8 flex flex-wrap justify-center gap-2'>
          <a
            href={`/${locale}/blog`}
            className={`rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 ${
              !searchParams.category
                ? 'bg-mystical-500 text-white'
                : 'bg-mystical-100 text-mystical-600 hover:bg-mystical-200 dark:bg-dark-700 dark:text-mystical-400 dark:hover:bg-dark-600'
            }`}
          >
            {t('allCategories')}
          </a>
          {categories.map(category => (
            <a
              key={category.id}
              href={`/${locale}/blog?category=${category.slug}`}
              className={`rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 ${
                searchParams.category === category.slug
                  ? 'bg-mystical-500 text-white'
                  : 'bg-mystical-100 text-mystical-600 hover:bg-mystical-200 dark:bg-dark-700 dark:text-mystical-400 dark:hover:bg-dark-600'
              }`}
            >
              {category.name}
            </a>
          ))}
        </nav>
      </header>

      {/* 主要内容区域 - Medium风格布局 */}
      <main className='mx-auto max-w-6xl px-4 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 gap-12 lg:grid-cols-3'>
          {/* 主内容区 */}
          <div className='lg:col-span-2'>
            <Suspense
              fallback={<BlogListSkeleton showFeatured={!!featuredPost} />}
            >
              <BlogList
                posts={posts}
                featuredPost={featuredPost}
                showFeatured={page === 1}
                layout='medium' // 使用Medium风格布局
                totalPosts={totalPosts}
                currentPage={page}
                totalPages={totalPages}
              />
            </Suspense>
          </div>

          {/* 侧边栏 */}
          <aside className='lg:col-span-1'>
            <div className='sticky top-8'>
              <BlogSidebar
                categories={categories}
                tags={tags}
                locale={locale}
                currentCategory={searchParams.category}
              />
            </div>
          </aside>
        </div>
      </main>
    </div>
  );
}

// 博客侧边栏组件 - 基于01规范的Medium风格设计
function BlogSidebar({
  categories,
  tags,
  locale,
  currentCategory,
}: {
  categories: BlogCategory[];
  tags: BlogTag[];
  locale: string;
  currentCategory?: string;
}) {
  return (
    <div className='space-y-6'>
      {/* 搜索框 - Medium风格 */}
      <div className='relative'>
        <input
          type='text'
          placeholder='Search articles...'
          className='focus:ring-3 w-full rounded-lg border border-mystical-200 bg-white px-4 py-3 pl-10 text-sm text-mystical-800 placeholder-mystical-400 transition-all focus:border-mystical-400 focus:outline-none focus:ring-mystical-100 dark:border-dark-600 dark:bg-dark-800 dark:text-mystical-200 dark:placeholder-mystical-500 dark:focus:ring-mystical-900'
        />
        <svg
          className='absolute left-3 top-3.5 h-4 w-4 text-mystical-400'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
          />
        </svg>
      </div>

      {/* 分类列表 */}
      <div>
        <h3 className='mb-4 text-lg font-bold text-mystical-900 dark:text-white'>
          Categories
        </h3>
        <div className='space-y-2'>
          {categories.map(category => (
            <a
              key={category.id}
              href={`/${locale}/blog/${category.slug}`}
              className={`block rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                currentCategory === category.slug
                  ? 'bg-mystical-100 text-mystical-700 dark:bg-dark-700 dark:text-mystical-300'
                  : 'text-mystical-600 hover:bg-mystical-50 hover:text-mystical-700 dark:text-mystical-400 dark:hover:bg-dark-800 dark:hover:text-mystical-300'
              }`}
            >
              {category.name}
              <span className='ml-2 text-xs text-mystical-400 dark:text-mystical-500'>
                ({category.postCount || 0})
              </span>
            </a>
          ))}
        </div>
      </div>

      {/* 热门文章 */}
      <div>
        <h3 className='mb-4 text-lg font-bold text-mystical-900 dark:text-white'>
          Popular Posts
        </h3>
        <div className='space-y-4'>
          {/* 这里应该显示热门文章，暂时用占位符 */}
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className='flex gap-3'>
              <span className='min-w-[20px] text-sm font-bold text-mystical-500'>
                {i.toString().padStart(2, '0')}
              </span>
              <div className='min-w-0 flex-1'>
                <h4 className='mb-1 line-clamp-2 text-sm font-medium leading-5 text-mystical-800 dark:text-mystical-200'>
                  Sample Article Title {i}
                </h4>
                <p className='text-xs text-mystical-500 dark:text-mystical-400'>
                  5 min read
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 标签云 */}
      <div>
        <h3 className='mb-4 text-lg font-bold text-mystical-900 dark:text-white'>
          Topics
        </h3>
        <div className='flex flex-wrap gap-2'>
          {tags.slice(0, 12).map(tag => (
            <a
              key={tag.id}
              href={`/${locale}/blog?tag=${tag.slug}`}
              className='inline-flex items-center rounded-full bg-mystical-100 px-3 py-1 text-xs font-medium text-mystical-600 transition-colors hover:bg-mystical-200 dark:bg-dark-700 dark:text-mystical-400 dark:hover:bg-dark-600'
            >
              {tag.name}
            </a>
          ))}
        </div>
      </div>

      {/* 订阅表单 - Medium风格 */}
      <div className='rounded-xl border border-mystical-200 bg-mystical-50 p-6 dark:border-dark-700 dark:bg-dark-800'>
        <h3 className='mb-2 text-lg font-bold text-mystical-900 dark:text-white'>
          Stay Updated
        </h3>
        <p className='mb-4 text-sm leading-relaxed text-mystical-600 dark:text-mystical-300'>
          Get the latest mystical insights and test content delivered to your
          inbox.
        </p>
        <form className='space-y-3'>
          <input
            type='email'
            placeholder='Enter your email'
            className='focus:ring-3 w-full rounded-lg border border-mystical-200 bg-white px-3 py-2 text-sm text-mystical-800 placeholder-mystical-400 transition-all focus:border-mystical-400 focus:outline-none focus:ring-mystical-100 dark:border-dark-600 dark:bg-dark-700 dark:text-mystical-200 dark:placeholder-mystical-500 dark:focus:ring-mystical-900'
          />
          <button
            type='submit'
            className='focus:ring-3 w-full rounded-lg bg-mystical-500 px-4 py-2 text-sm font-medium text-white transition-all hover:bg-mystical-600 focus:outline-none focus:ring-mystical-100 dark:focus:ring-mystical-900'
          >
            Subscribe
          </button>
        </form>
      </div>
    </div>
  );
}
