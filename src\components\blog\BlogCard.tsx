'use client';

import { Calendar, Clock, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

interface BlogCardProps {
  post: BlogPost;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
  showAuthor?: boolean;

  showExcerpt?: boolean;
}

export function BlogCard({
  post,
  variant = 'default',
  className,
  showAuthor = true,

  showExcerpt = true,
}: BlogCardProps) {
  const t = useTranslations('blog');

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(post.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  const cardClasses = cn(
    'group relative overflow-hidden rounded-xl border border-mystical-200 dark:border-dark-700',
    'bg-white dark:bg-dark-800 transition-all duration-300 ease-out',
    'hover:transform hover:-translate-y-2 hover:shadow-mystical-lg',
    {
      'p-6': variant === 'default',
      'p-8 bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700':
        variant === 'featured',
      'p-4': variant === 'compact',
    },
    className
  );

  const imageClasses = cn(
    'w-full object-cover transition-transform duration-300 group-hover:scale-105',
    {
      'aspect-[16/9] rounded-lg mb-6': variant === 'default',
      'aspect-[16/9] rounded-xl mb-8': variant === 'featured',
      'aspect-[16/9] rounded-lg mb-4': variant === 'compact',
    }
  );

  const titleClasses = cn(
    'font-bold font-serif text-mystical-900 dark:text-white line-clamp-2',
    'group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors',
    {
      'text-xl mb-3': variant === 'default',
      'text-2xl lg:text-3xl mb-4': variant === 'featured',
      'text-lg mb-2': variant === 'compact',
    }
  );

  const excerptClasses = cn(
    'text-mystical-600 dark:text-mystical-300 line-clamp-3',
    {
      'text-base mb-4': variant === 'default',
      'text-lg mb-6': variant === 'featured',
      'text-sm mb-3': variant === 'compact',
    }
  );

  return (
    <article className={cardClasses}>
      {/* Cover Image */}
      {post.coverImage && (
        <div className='relative overflow-hidden rounded-lg'>
          <Link href={`/blog/${post.category.slug}/${post.slug}`}>
            <Image
              src={post.coverImage}
              alt={post.title}
              width={variant === 'featured' ? 800 : 600}
              height={variant === 'featured' ? 450 : 337}
              className={imageClasses}
              sizes={
                variant === 'featured'
                  ? '(max-width: 768px) 100vw, 800px'
                  : '(max-width: 768px) 100vw, 600px'
              }
            />
          </Link>
        </div>
      )}

      {/* Category Badge */}
      {post.categoryData && (
        <div className='mb-3'>
          <Link
            href={`/blog/${post.categoryData.slug}`}
            className={cn(
              'inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold',
              'bg-mystical-100 text-mystical-700 dark:bg-dark-700 dark:text-mystical-300',
              'transition-colors hover:bg-mystical-200 dark:hover:bg-dark-600',
              'uppercase tracking-wide'
            )}
          >
            {post.categoryData.name}
          </Link>
        </div>
      )}

      {/* Title */}
      <h2 className={titleClasses}>
        <Link
          href={`/blog/${post.categoryData?.slug || post.category}/${post.slug}`}
        >
          {post.title}
        </Link>
      </h2>

      {/* Excerpt */}
      {showExcerpt && <p className={excerptClasses}>{post.excerpt}</p>}

      {/* Author Info */}
      {showAuthor && post.author && (
        <div className='mb-4 flex items-center gap-3'>
          {post.author.avatar ? (
            <Image
              src={post.author.avatar}
              alt={post.author.name}
              width={32}
              height={32}
              className='rounded-full border-2 border-mystical-200 dark:border-dark-600'
            />
          ) : (
            <div className='flex h-8 w-8 items-center justify-center rounded-full border-2 border-mystical-200 bg-mystical-200 dark:border-dark-600 dark:bg-dark-600'>
              <User className='h-4 w-4 text-mystical-500 dark:text-mystical-400' />
            </div>
          )}
          <div className='min-w-0 flex-1'>
            <p className='truncate text-sm font-medium text-mystical-900 dark:text-white'>
              {post.author.name}
            </p>
          </div>
        </div>
      )}

      {/* Meta Information */}
      <div className='flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-1'>
            <Calendar className='h-3 w-3' />
            <span>{formatDate(post.publishedAt || post.createdAt)}</span>
          </div>
          <div className='flex items-center gap-1'>
            <Clock className='h-3 w-3' />
            <span>
              {post.readingTime} {t('readTime')}
            </span>
          </div>
        </div>
      </div>

      {/* Tags */}
      {post.tags.length > 0 && variant !== 'compact' && (
        <div className='mt-4 flex flex-wrap gap-2'>
          {post.tags.slice(0, 3).map(tag => (
            <Link
              key={tag.id}
              href={`/blog/tag/${tag.slug}`}
              className={cn(
                'inline-flex items-center rounded-md px-2 py-1 text-xs',
                'bg-mystical-50 text-mystical-600 dark:bg-dark-700 dark:text-mystical-400',
                'transition-colors hover:bg-mystical-100 dark:hover:bg-dark-600'
              )}
            >
              #{tag.name}
            </Link>
          ))}
          {post.tags.length > 3 && (
            <span className='text-xs text-mystical-500 dark:text-mystical-400'>
              +{post.tags.length - 3} {t('moreTags')}
            </span>
          )}
        </div>
      )}

      {/* Featured Badge */}
      {post.featured && (
        <div className='absolute right-4 top-4'>
          <div className='rounded-full bg-gold-500 px-2 py-1 text-xs font-semibold text-white'>
            {t('featured')}
          </div>
        </div>
      )}

      {/* Read More Link */}
      <div className='mt-4'>
        <Link
          href={`/blog/${post.categoryData?.slug || post.category}/${post.slug}`}
          className={cn(
            'inline-flex items-center text-sm font-medium text-mystical-600 dark:text-mystical-400',
            'transition-colors hover:text-mystical-700 dark:hover:text-mystical-300'
          )}
        >
          {t('readMore')} →
        </Link>
      </div>
    </article>
  );
}
