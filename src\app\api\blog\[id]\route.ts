/**
 * 单个博客文章API路由 - /api/blog/[id]
 * 支持获取、更新、删除单个博客文章
 */

import { NextRequest, NextResponse } from 'next/server';

import { DatabaseService } from '@/lib/database-service';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || undefined;

    // 如果ID看起来像slug，则按slug查询
    let post;
    if (id.includes('-') || id.length > 25) {
      post = await DatabaseService.getBlogPostBySlug(id, locale);
    } else {
      // 按ID查询（需要添加这个方法到DatabaseService）
      const posts = await DatabaseService.getBlogPosts({ limit: 1 });
      post = posts.find(p => p.id === id);
    }

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          error: 'Blog post not found',
        },
        { status: 404 }
      );
    }

    // 增加浏览量
    try {
      await DatabaseService.incrementViewCount(post.id);
    } catch (error) {
      console.warn('Failed to increment view count:', error);
    }

    return NextResponse.json({
      success: true,
      data: post,
    });
  } catch (error: any) {
    console.error('Error fetching blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog post',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const body = await request.json();

    // 如果更新了标题但没有更新slug，自动生成新的slug
    if (body.title && !body.slug) {
      body.slug = generateSlug(body.title);
    }

    // 如果更新了内容但没有更新阅读时间，重新计算
    if (body.content && !body.readingTime) {
      body.readingTime = calculateReadingTime(body.content);
    }

    // 如果状态改为已发布且没有发布时间，设置发布时间
    if (body.status === 'PUBLISHED' && !body.publishedAt) {
      body.publishedAt = new Date();
    }

    const post = await DatabaseService.updateBlogPost(id, body);

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Blog post updated successfully',
    });
  } catch (error: any) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update blog post',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;

    await DatabaseService.deleteBlogPost(id);

    return NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully',
    });
  } catch (error: any) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete blog post',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// 辅助函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 100);
}

function calculateReadingTime(content: string): number {
  const plainText = content.replace(/<[^>]*>/g, '');
  const wordsPerMinute = 200;
  const wordCount = plainText.length;
  return Math.ceil(wordCount / wordsPerMinute);
}
